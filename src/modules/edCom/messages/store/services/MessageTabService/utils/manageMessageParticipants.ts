import { HumanError, IContext } from 'edana-microservice';
import { isEmpty } from 'lodash';
import {
  ACTIVE,
  ACTIVE_ID,
  DELETED_ID,
} from '../../../../../../../model/Status';
import IMessage from '../../../../abstract/IMessage';
import {
  SERVICE_MESSAGE_ADD_USERS,
  SERVICE_MESSAGE_REMOVE_USERS,
} from '../../../../const';
import { CHAT, isBroadcast } from '../../../../model/MessageType';
import MessageParticipant from '../../../entities/MessageParticipant';
import { sendServiceMessage } from '../../ServiceMessagesService';
import TMessageParticipantType from '../../../../abstract/TMessageParticipantType';
import MessageParticipantType from '../../../../model/MessageParticipantType';
import { SUBMISSION_CHAT } from '../../../../model/MessageModuleArea';

export const MESSAGE_PERSON_MIN_LIMIT = 2;

export default async function manageMessageParticipants(
  context: IContext,
  {
    participantsAdded,
    participantsRemoved,
    message,
    userId,
    tenantId,
    isNew,
    participantType = MessageParticipantType.PARTICIPANT,
  }: IManageMessageParticipantsParams,
) {
  await deleteParticipants(context, {
    participantsRemoved,
    message,
    userId,
    tenantId,
    isNew,
  });

  await addParticipants(context, {
    participantType,
    participantsAdded,
    message,
    userId,
    tenantId,
    isNew,
  });

  await checkParticipantsCount(context, {
    tenantId,
    message,
    participantType,
  });
}

export const deleteParticipants = async (
  context: IContext,
  {
    message,
    userId,
    isNew,
    tenantId,
    participantsRemoved,
  }: {
    participantsRemoved: number[];
    message: IMessage;
    userId: number | null;
    tenantId: number;
    isNew: boolean;
  },
): Promise<void> => {
  for (const personId of participantsRemoved) {
    const query = `UPDATE EC_MESSAGE_PARTICIPANT EMP
                   SET EMP.STATUS = :deletedStatus
                   WHERE MESSAGE_ID = :messageId
                     AND TENANT_ID = :tenantId
                     AND PERSON_ID = :personId
                     AND STATUS = :statusId`;

    await context.connection.execute(query, {
      messageId: message.id,
      tenantId,
      personId,
      statusId: ACTIVE_ID,
      deletedStatus: DELETED_ID,
    });
  }

  if (
    !isNew &&
    participantsRemoved &&
    !isEmpty(participantsRemoved) &&
    userId
  ) {
    await sendServiceMessage(context, {
      messageId: message.id,
      content: SERVICE_MESSAGE_REMOVE_USERS,
      tenantId,
      personId: userId,
      activityPayload: {
        authorId: userId,
        batchIds: participantsRemoved,
      },
    });
  }
};

const addParticipants = async (
  context: IContext,
  {
    participantType,
    participantsAdded,
    message,
    userId,
    tenantId,
    isNew,
  }: {
    participantType: TMessageParticipantType;
    participantsAdded: number[];
    message: IMessage;
    userId: number | null;
    tenantId: number;
    isNew: boolean;
  },
): Promise<void> => {
  for (const personId of participantsAdded) {
    const _messageParticipant = await MessageParticipant.findBy(
      context.connection,
      {
        messageId: message.id,
        personId,
        status: ACTIVE,
        tenantId,
      },
      {},
      { allowNull: true },
    );

    if (!_messageParticipant) {
      await MessageParticipant.create(context, {
        messageId: message.id,
        personId,
        status: ACTIVE,
        tenantId,
        participantType,
      });
    }
  }

  if (!isNew && participantsAdded && !isEmpty(participantsAdded) && userId) {
    await sendServiceMessage(context, {
      content: SERVICE_MESSAGE_ADD_USERS,
      messageId: message.id,
      tenantId,
      personId: userId,
      activityPayload: {
        authorId: userId,
        batchIds: participantsAdded,
      },
    });
  }
};

const checkParticipantsCount = async (
  context: IContext,
  {
    message,
    tenantId,
    participantType,
  }: {
    message: IMessage;
    tenantId: number;
    participantType: TMessageParticipantType;
  },
): Promise<void> => {
  // for broadcast and chat we should have at least 2 members
  if (
    (isBroadcast(message) || message.type === CHAT) &&
    message.moduleAreaId !== SUBMISSION_CHAT &&
    participantType === MessageParticipantType.PARTICIPANT
  ) {
    const count = await MessageParticipant.loadCountBy(context.connection, {
      status: ACTIVE,
      messageId: message.id,
      tenantId,
    });

    if (count < MESSAGE_PERSON_MIN_LIMIT) {
      throw new HumanError('moreParticipants');
    }
  }
};

interface IManageMessageParticipantsParams {
  participantsAdded: number[];
  participantsRemoved: number[];
  participantType?: TMessageParticipantType;
  message: IMessage;
  userId: number | null;
  tenantId: number;
  isNew: boolean;
}
