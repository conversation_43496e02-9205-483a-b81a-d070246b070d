import { DeleteResponse, HumanError, IContext } from 'edana-microservice';
import { DELETED, INACTIVE } from '../../../../../../model/Status';
import IMessage from '../../../abstract/IMessage';
import { SERVICE_MESSAGE_DELETE_MESSAGE } from '../../../const';
import {
  isBroadcastOrChannel,
  isPrivateChat,
} from '../../../model/MessageType';
import Message from '../../entities/Message';
import { sendServiceMessage } from '../ServiceMessagesService';
import { deleteMessagePrivateMessage } from './deleteMessagePrivateMessage';
import { checkMessageActive } from './utils/checkMessageActive';
import { deleteParticipants } from '../MessageTabService/utils/manageMessageParticipants';

export const deleteMessage = async (
  context: IContext,
  { id, tenantId, userId, forEveryone }: IDeleteMessageParams,
): Promise<DeleteResponse> => {
  const message: IMessage = await checkMessageActive(context, {
    messageId: id,
    tenantId,
  });

  if (isBroadcastOrChannel(message) && !forEveryone) {
    throw new HumanError('cantDelete');
  }

  if (isPrivateChat(message)) {
    return await deleteMessagePrivateMessage(context, {
      forEveryone,
      id,
      tenantId,
      userId,
    });
  }

  await Message.update(context, {
    tenantId,
    id,
    status: forEveryone ? DELETED : INACTIVE,
  });

  if (userId) {
    await sendServiceMessage(context, {
      content: SERVICE_MESSAGE_DELETE_MESSAGE,
      tenantId,
      messageId: id,
      personId: userId,
      activityPayload: {
        authorId: userId,
      },
    });
  }

  if (!forEveryone && userId) {
    await deleteParticipants(context, {
      participantsRemoved: [userId],
      message,
      userId,
      tenantId,
      isNew: true,
    });
    await context.connection.commit();
  }
  return new DeleteResponse(message.id);
};

interface IDeleteMessageParams {
  id: number;
  tenantId: number;
  userId: number | null;
  forEveryone?: boolean;
}
