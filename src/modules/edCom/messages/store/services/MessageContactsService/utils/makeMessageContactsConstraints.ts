import { IContext, TStatus } from 'edana-microservice';
import { includes, uniq } from 'lodash';
import { ACTIVE, ACTIVE_ID } from '../../../../../../../model/Status';
import { uniqMap } from '../../../../../../core/helpers';
import ContactIndividual from '../../../../../contacts/store/entities/ContactIndividual';
import TMessageContactType from '../../../../abstract/TMessageContactType';
import { INDIVIDUALS, SUGGESTED } from '../../../../model/MessageContactType';
import MessageParticipant from '../../../entities/MessageParticipant';
import Message from '../../../entities/Message';
// import { PRIVATE_CHAT_ID } from '../../../../../../../modules/edCom/messages/model/MessageType';

export default async function makeMessageContactsConstraints(
  context: IContext,
  {
    entityTypeIds,
    ownerPersonId,
    tenantId,
    types,
    searchQuery,
    organisationGroupId,
  }: IMakeConstraintsInput,
): Promise<{
  filterQuery: string;
  status: TStatus;
  sortOrder: string;
  sortKey: string;
  personEntityTypeIds: number[];
  ids: number[];
  organisationGroupIds?: number[];
}> {
  const ids: number[] = [];

  if (includes(types, INDIVIDUALS)) {
    const contactIndividuals = await ContactIndividual.loadBy(
      context.connection,
      {
        ownerPersonId,
        tenantId,
        status: ACTIVE,
      },
    );

    ids.push(...uniqMap(contactIndividuals, 'contactPersonId'));
  }

  if (includes(types, SUGGESTED)) {
    const messageParticipants = await MessageParticipant.loadWithQuery(
      context.connection,
      `select ECP2.*
         from ${MessageParticipant.table} ECP
                  inner join ${MessageParticipant.table} ECP2 ON
                 ECP.MESSAGE_ID = ECP2.MESSAGE_ID AND
                 ECP.PERSON_ID <> ECP2.PERSON_ID AND
                 ECP.TENANT_ID = ECP2.TENANT_ID
         where ECP.PERSON_ID = :personId
           and ECP.TENANT_ID = :tenantId`,
      {
        personId: ownerPersonId,
        tenantId,
      },
    );

    ids.push(...uniqMap(messageParticipants, 'personId'));
  }

  // Get all unique person IDs
  const allIds = uniq(ids);

  // If we have person IDs, get them sorted by latest message updated date
  let sortedIds = allIds;
  let hasMessageData = false;

  if (allIds.length > 0) {
    const sortedPersons = await context.connection.execute<{
      ID: number;
      LATEST_MESSAGE_DATE: Date;
      HAS_MESSAGE: number;
    }>(
      `SELECT DISTINCT p.ID,
              -- COALESCE(latest_msg.LATEST_UPDATED_AT, TO_DATE('1900-01-01', 'YYYY-MM-DD')) as LATEST_MESSAGE_DATE,
              LATEST_UPDATED_AT as LATEST_MESSAGE_DATE,
              CASE WHEN latest_msg.LATEST_UPDATED_AT IS NOT NULL THEN 1 ELSE 0 END as HAS_MESSAGE
       FROM C_PERSON p
       LEFT JOIN (
         SELECT mp.PERSON_ID,
                MAX(COALESCE(m.UPDATED_AT, m.CREATED_AT)) as LATEST_UPDATED_AT
         FROM ${MessageParticipant.table} mp
         INNER JOIN ${
           MessageParticipant.table
         } mp_owner ON mp.MESSAGE_ID = mp_owner.MESSAGE_ID
         INNER JOIN ${Message.table} m ON mp.MESSAGE_ID = m.ID
         INNER JOIN EC_MESSAGE_ITEM mi ON mi.MESSAGE_ID = m.ID
         WHERE mp_owner.PERSON_ID = :ownerPersonId
           AND mp.PERSON_ID != :ownerPersonId
           AND mp.TENANT_ID = :tenantId
           AND mp.STATUS = :activeStatus
           AND m.STATUS = :activeStatus
           -- AND m.TYPE = :messageType
         GROUP BY mp.PERSON_ID
       ) latest_msg ON p.ID = latest_msg.PERSON_ID
       WHERE p.ID IN (${allIds.join(',')})
       ORDER BY
         HAS_MESSAGE DESC,
         LATEST_MESSAGE_DATE DESC
         -- P.FIRST_NAME, P.LAST_NAME ASC`,
      {
        ownerPersonId,
        tenantId,
        activeStatus: ACTIVE_ID,
        // messageType: PRIVATE_CHAT_ID,
      },
    );
    if (sortedPersons.rows && sortedPersons.rows.length > 0) {
      sortedIds = sortedPersons.rows.map(
        (row: { ID: number; HAS_MESSAGE: number }) => row.ID,
      );
      hasMessageData = sortedPersons.rows.some(
        (row: { HAS_MESSAGE: number }) => row.HAS_MESSAGE === 1,
      );
    }
  }

  // If we have message data, create a custom ORDER BY clause that maintains the sorted order
  // Otherwise, fall back to sorting by LAST_NAME
  let sortKey = 'LAST_NAME';
  const sortOrder = 'ASC';

  if (hasMessageData && sortedIds.length > 0) {
    const caseStatements = sortedIds
      .map((id, index) => `WHEN ${id} THEN ${index}`)
      .join(' ');
    sortKey = `CASE ID ${caseStatements} END`;
  }

  return {
    filterQuery: searchQuery,
    status: ACTIVE,
    sortOrder,
    sortKey,
    personEntityTypeIds: entityTypeIds,
    ids: sortedIds,
    organisationGroupIds: organisationGroupId
      ? [organisationGroupId]
      : undefined,
  };
}

interface IMakeConstraintsInput {
  types: TMessageContactType[];
  ownerPersonId: number;
  tenantId: number;
  entityTypeIds: number[];
  searchQuery: string;
  organisationGroupId?: number;
}
