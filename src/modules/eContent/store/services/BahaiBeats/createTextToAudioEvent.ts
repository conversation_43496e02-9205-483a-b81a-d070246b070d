import { IContext, DateTime } from 'edana-microservice';

import TextToAudioEvent, {
  ITextToAudioEvent,
} from '../../entities/TextToAudioEvent';
import Person from '../../../../../modules/core/store/entities/Person';
import { getAllTenants } from '../../../../../modules/core';
import { keyBy } from 'lodash';

const createTextToAudioEvent = async (
  context: IContext,
  params: ICreateTextToAudioEventInput,
): Promise<ITextToAudioEvent> => {
  const { tenantId, connection } = context;

  const person = await Person.findBy(connection, [
    {
      id: params.createdBy,
    },
  ]);
  const tenants = await getAllTenants(context);
  const tenantById = keyBy(tenants, 'id');
  const response = await TextToAudioEvent.create(connection, {
    ...params,
    tenantId,
    searchTerms: `${person.id}; ${person.firstName}; ${person.middleName}; ${
      person.lastName
    }; ${person.firstName} ${person.middleName} ${person.lastName}; ${
      person.middleName
    } ${person.lastName}; ${tenantById[tenantId]?.siteLink}; ${
      PLATFORMS[params.platformId]
    };`,
    createdAt: DateTime.utc(),
  });

  await connection.commit();

  return response;
};

export default createTextToAudioEvent;

interface ICreateTextToAudioEventInput {
  platformId: number;
  description: string;
  totalChars: number;
  createdBy: number;
  createdAt?: DateTime;
}

const PLATFORMS = {
  101: 'Google',
  102: 'OpenAI',
  103: 'GennyLovo',
};
