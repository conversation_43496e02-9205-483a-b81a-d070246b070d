import { IContext } from 'edana-microservice';
import { join, split, reduce } from 'lodash';

import EContentLibrary from '../../entities/EContentLibrary';
import EContentContent from '../../entities/EContentContent';
import EContentResource from '../../entities/EContentResource';
import EContentItem from '../../entities/EContentItem';
import EContentResourceXAttribute from '../../entities/EContentResourceXAttribute';
import EContentClob from '../../entities/EContentClob';
import {
  audioFileKeywordType,
  contentKeywordType,
  documentKeywordType,
  imageKeywordType,
  textKeywordType,
  topicKeywordType,
  videoFileKeywordType,
  questionKeywordType,
} from '../../../model/EContentResourceXAttributeTypes';

const CONTENT_TABLE = EContentContent.table;
const RESOURCE = EContentResource.table;
const ITEM = EContentItem.table;
const RESOURCE_X_ATTRIBUTE = EContentResourceXAttribute.table;
const CONTENT_CLOB = EContentClob.table;
const LIBRARY = EContentLibrary.table;

export default async function eContentLibrariesKeywords(
  { connection }: IContext,
  { librariesIds, tenantId }: { librariesIds: number[]; tenantId: number },
): Promise<Record<number, string[]>> {
  const attributesForSearch = [
    topicKeywordType.id,
    contentKeywordType.id,
    textKeywordType.id,
    documentKeywordType.id,
    imageKeywordType.id,
    videoFileKeywordType.id,
    audioFileKeywordType.id,
    questionKeywordType.id,
  ];

  const { rows } = await connection.execute(
    `select L.id as library_id, CL.content from ${LIBRARY} L
          join ${RESOURCE} R on r.library_id = L.ID
          join ${ITEM} I on i.resource_id = r.id
          join ${RESOURCE_X_ATTRIBUTE} X 
            on x.resource_id = i.resource_id 
            and X.attribute_id in(${join(attributesForSearch, ',')})
          join ${CONTENT_TABLE} C on c.item_id = i.id
          join ${CONTENT_CLOB} CL 
            on CL.content_id = c.id 
            and cl.resource_attribute_id = x.id
          where L.id in(${join(librariesIds, ',')})
          and L.tenant_id = :tenantId
          and cl.content is not null`,
    { tenantId },
  );

  return reduce(
    rows as { LIBRARY_ID: number; CONTENT: string }[],
    (acc, { LIBRARY_ID, CONTENT }) => {
      if (acc[LIBRARY_ID]) {
        acc[LIBRARY_ID].push(...split(CONTENT, ','));
      } else {
        acc[LIBRARY_ID] = split(CONTENT, ',');
      }
      return acc;
    },
    {},
  );
}
