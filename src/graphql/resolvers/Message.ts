import { DateTime, IContext, TStatus } from 'edana-microservice';
import { createBatchResolver } from 'graphql-resolve-batch';
import { filter, includes, isEmpty, keyBy, map } from 'lodash';
import { ED_COM_CHAT_ATTACHMENTS } from '../../fsCategoriesAreas';
import { getPersons } from '../../modules/core';
import { uniqMap } from '../../modules/core/helpers';
import getLastMessages from '../../modules/edCom/messages/endpoints/getLastMessages';
import isMessageOwnerBulk from '../../modules/edCom/messages/endpoints/isMessageOwnerBulk';
import getParticipantsCount from '../../modules/edCom/messages/endpoints/getParticipantsCount';
import getRecipientPersons from '../../modules/edCom/messages/endpoints/getRecipientPersons';
import getUnreadMessagesCount from '../../modules/edCom/messages/endpoints/getUnreadMessagesCount';
import TMessageType from '../abstract/TMessageType';
import bulkFirstAttachment from './utils/bulkFirstAttachment';
import { getOrganisationAllocationNames } from '../../modules/core/store/services/OrganisationsService';
import Class from '../../modules/programs/store/entities/Class';
import LearningSpaceLPTask from '../../modules/programs/store/entities/LearningSpaceLPTask';
import OrgStructureLevel from '../../modules/core/store/entities/OrgStructureLevel';

export interface IMessageRaw {
  __type: 'Message';
  id: number;
  tenantId: number;
  personEntityAllocationId: number | null;
  organisationId: number | null;
  type: TMessageType;
  name: string;
  createdBy: number | null;
  createdAt: DateTime;
  status: TStatus;
  orgStructureLevelId?: number;
  organisationGroupId?: number;
  referenceId?: number;
}

export const isMessageDisabled = createBatchResolver(
  async (nodes: IMessageRaw[], args: undefined, context: IContext) => {
    const isClassGroupChatFlags = nodes.map(async node => {
      if (!node.orgStructureLevelId) return false;
      if (node.referenceId) {
        try {
          const classExists = await Class.findBy(context.connection, {
            id: node.referenceId,
            tenantId: context.tenantId,
          });
          if (classExists) return true;
          // eslint-disable-next-line no-empty
        } catch {}
        try {
          const taskExists = await LearningSpaceLPTask.findBy(
            context.connection,
            {
              id: node.referenceId,
              tenantId: context.tenantId,
            },
          );
          if (taskExists) return true;
          // eslint-disable-next-line no-empty
        } catch {}
        return false;
      }
      return false;
    });

    return map(nodes, (_, idx) => isClassGroupChatFlags[idx]);
  },
);

export const organisationAllocationNames = createBatchResolver(
  async (nodes: IMessageRaw[], args: undefined, context: IContext) => {
    const { tenantId } = context;

    const allocationPromises = nodes.map(async node => {
      const { organisationGroupId, orgStructureLevelId, organisationId } = node;
      if (!orgStructureLevelId) return false;

      const orgStructureLevel = await OrgStructureLevel.findBy(
        context.connection,
        {
          id: orgStructureLevelId,
          tenantId,
        },
        {},
        { allowNull: true },
      );

      if (!orgStructureLevel) {
        return null; // or handle this case as needed
      }

      const allocationNamesResponse = await getOrganisationAllocationNames(
        context.connection,
        {
          organisationId: organisationId as number,
          orgGroupId: organisationGroupId as number,
          orgStructureLevelId: orgStructureLevelId as number,
          organisationStructureId: [orgStructureLevel.organisationStructureId],
          tenantId,
          fetchOrganisations: true,
        },
      );

      return allocationNamesResponse; // Return the response for this node
    });

    const allocationNamesResults = await Promise.all(allocationPromises);

    return map(nodes, (_, idx) => allocationNamesResults[idx]);
  },
);

export const lastMessageItem = createBatchResolver(
  async (nodes: IMessageRaw[], args: undefined, context: IContext) => {
    const { tenantId, userId } = context;

    const lastMessages = await getLastMessages(context, {
      tenantId,
      personId: userId,
      messageIds: uniqMap(nodes),
    });

    const lastMessagesMapped = keyBy(lastMessages, 'messageId');

    return map(nodes, ({ id }) => lastMessagesMapped[id]);
  },
);

export const isOwner = createBatchResolver(
  async (messages: IMessageRaw[], args: undefined, context: IContext) => {
    const { tenantId, userId } = context;
    const messagesOwnerDict = await isMessageOwnerBulk(context, {
      tenantId,
      personId: userId,
      messageIds: map(messages, 'id'),
    });

    return map(
      messages,
      ({ id, createdBy }) => !!(createdBy === userId || messagesOwnerDict[id]),
    );
  },
);

export const createdByPerson = createBatchResolver(
  async (nodes: IMessageRaw[], args: undefined, context: IContext) => {
    const { tenantId } = context;

    const persons = await getPersons(context, {
      tenantId,
      ids: filter(uniqMap(nodes, 'createdBy')),
    });

    const personsMapped = keyBy(persons, 'id');

    return map(nodes, ({ createdBy }) => createdBy && personsMapped[createdBy]);
  },
);

export const participantsCount = createBatchResolver(
  async (
    messages: IMessageRaw[],
    args: undefined,
    context: IContext,
  ): Promise<number[]> => {
    const { tenantId } = context;
    const participantsCounts = await getParticipantsCount(context, {
      tenantId,
      messageId: map(messages, 'id'),
    });

    const participantsCountsHash = keyBy(participantsCounts, 'groupId');

    return map(messages, ({ id }) => participantsCountsHash[id]?.count || 0);
  },
);

export const unreadMessagesCount = createBatchResolver(
  async (messages, {}, context: IContext) => {
    const messageIds = map(messages, 'id');
    const res = await getUnreadMessagesCount(context, {
      tenantId: context.tenantId,
      userId: context.userId,
      messageIds,
    });

    const messageItemsUnreadCountHash = keyBy(res, 'messageId');

    return map(
      messages,
      ({ id }) => messageItemsUnreadCountHash[id]?.unreadCount || 0,
    );
  },
);

export const recipientPerson = createBatchResolver(
  async (nodes: IMessageRaw[], args: undefined, context: IContext) => {
    const { tenantId, userId } = context;

    const messageIds = uniqMap(
      filter(nodes, ({ type }) => includes(['PRIVATE_CHAT', 'CHAT'], type)),
    );

    if (isEmpty(messageIds)) {
      return map(nodes, () => undefined);
    }

    const recipientPersons = await getRecipientPersons(context, {
      tenantId,
      personId: userId,
      messageIds,
    });

    const recipientPersonsMapped = keyBy(recipientPersons, 'messageId');

    const persons = await getPersons(context, {
      tenantId,
      ids: uniqMap(recipientPersons, 'personId'),
    });

    const personsMapped = keyBy(persons, 'id');

    return map(
      nodes,
      ({ id }) => personsMapped[recipientPersonsMapped[id]?.personId],
    );
  },
);

export const thumbnail = bulkFirstAttachment(ED_COM_CHAT_ATTACHMENTS);
